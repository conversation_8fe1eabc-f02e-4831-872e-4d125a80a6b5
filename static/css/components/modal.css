/* 模态框组件样式 - 使用设计令牌系统 */

/* 模态框显示状态 */
.modal {
    display: none;
}

.modal.show {
    display: flex !important;
    align-items: center;
    justify-content: center;
    padding: var(--modal-padding);
}

/* 模态框动画 */
@keyframes fade-in {
    from { 
        opacity: 0; 
    }
    to { 
        opacity: 1; 
    }
}

@keyframes slide-in {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes modal-slide-in {
    from {
        transform: translateY(-50px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

/* 动画类 */
.animate-fade-in {
    animation: fade-in var(--transition-slow) var(--ease-out);
}

.animate-slide-in {
    animation: slide-in 0.6s var(--ease-out);
}

.animate-modal-slide-in {
    animation: modal-slide-in var(--transition-slow) var(--ease-out);
}

/* 模态框容器样式 */
.modal-container {
    width: var(--modal-width);
    min-width: var(--modal-min-width);
    max-width: var(--modal-max-width);
    max-height: var(--modal-max-height);
    border-radius: var(--modal-border-radius);
}

/* 模态框头部 */
.modal-header {
    padding: var(--modal-header-padding);
}

/* 模态框时间设置区域 */
.modal-time-section {
    padding: var(--modal-section-padding);
}

/* 模态框内容区域 */
.modal-content {
    padding: var(--modal-content-padding);
}

/* 统一模态框关闭按钮样式 */
.modal .close-button,
.modal button[data-dismiss="modal"]:not(.custom-modal-button) {
    width: 32px;
    height: 32px;
    min-width: 32px;
    min-height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all var(--transition-normal) var(--ease-out);
    font-weight: var(--font-weight-normal);
    border: none;
    color: var(--color-text-secondary);
    cursor: pointer;
}

.modal .close-button:hover,
.modal button[data-dismiss="modal"]:not(.custom-modal-button):hover {
    transform: scale(1.1);
    background: var(--color-gray-200);
    color: var(--color-text-primary);
}

.modal .close-button:focus,
.modal button[data-dismiss="modal"]:not(.custom-modal-button):focus {
    outline: 2px solid var(--color-primary-500);
    outline-offset: 2px;
}

/* 响应式模态框样式 */
/* 平板端优化 (768px - 1024px) */
@media (max-width: 1024px) and (min-width: 769px) {
    :root {
        --modal-width: 85%;
        --modal-max-width: 800px;
        --modal-padding: var(--space-3);
        --modal-header-padding: var(--space-5) var(--space-5) var(--space-3) var(--space-5);
        --modal-content-padding: 0 var(--space-5) var(--space-5) var(--space-5);
        --modal-section-padding: var(--space-2) var(--space-5);
    }
}

/* 移动端优化 (481px - 768px) */
@media (max-width: 768px) and (min-width: 481px) {
    :root {
        --modal-width: 95%;
        --modal-max-width: 95vw;
        --modal-min-width: 320px;
        --modal-max-height: 90vh; /* 增加5vh：从85vh到90vh */
        --modal-padding: var(--space-3);
        --modal-header-padding: var(--space-4) var(--space-4) var(--space-3) var(--space-4);
        --modal-content-padding: 0 var(--space-4) var(--space-4) var(--space-4);
        --modal-section-padding: var(--space-2) var(--space-4);
    }
}

/* 小屏幕移动端优化 (≤480px) */
@media (max-width: 480px) {
    :root {
        --modal-width: 98%;
        --modal-max-width: 98vw;
        --modal-min-width: 280px;
        --modal-max-height: 93vh; /* 增加3vh：从90vh到93vh */
        --modal-padding: var(--space-2);
        --modal-header-padding: var(--space-3) var(--space-3) var(--space-2) var(--space-3);
        --modal-content-padding: 0 var(--space-3) var(--space-3) var(--space-3);
        --modal-section-padding: var(--space-2) var(--space-3);
    }
}

/* 极小屏幕优化 (≤360px) */
@media (max-width: 360px) {
    :root {
        --modal-width: 100%;
        --modal-max-width: 100vw;
        --modal-min-width: 280px;
        --modal-max-height: 95vh;
        --modal-padding: var(--space-1);
        --modal-header-padding: var(--space-2) var(--space-2) var(--space-1) var(--space-2);
        --modal-content-padding: 0 var(--space-2) var(--space-2) var(--space-2);
        --modal-section-padding: var(--space-1) var(--space-2);
    }

    .modal-container {
        border-radius: var(--border-radius-lg);
    }
}

/* 模态框内部元素的响应式优化 */
/* 编辑器区域 */
.modal-content textarea {
    padding: var(--space-5);
    font-size: var(--text-base);
    line-height: var(--line-height-normal);
}

/* 标签输入区域 */
.modal-content .tags-input-wrapper {
    padding: var(--space-2);
    margin-top: var(--space-2);
}

.modal-content .tags-input-wrapper input {
    padding: var(--space-2);
    font-size: var(--text-base);
}

/* 移动端编辑器和标签优化 */
@media (max-width: 768px) {
    .modal-content textarea {
        padding: var(--space-4);
        font-size: max(16px, var(--text-base)); /* 防止iOS自动缩放 */
    }

    .modal-content .tags-input-wrapper {
        padding: var(--space-2);
    }

    .modal-content .tags-input-wrapper input {
        padding: var(--space-2);
        font-size: max(16px, var(--text-base)); /* 防止iOS自动缩放 */
    }
}

@media (max-width: 480px) {
    .modal-content textarea {
        padding: var(--space-3);
    }

    .modal-content .tags-input-wrapper {
        padding: var(--space-1);
    }

    .modal-content .tags-input-wrapper input {
        padding: var(--space-1);
    }
}
