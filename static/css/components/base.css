/* 组件基础样式 - 使用设计令牌系统 */

/* 自定义 min-height 类 */
.min-h-250 {
    min-height: 320px; /* 增加70px：从250px到320px */
}

.min-h-300 {
    min-height: 380px; /* 增加80px：从300px到380px */
}

/* 响应式 min-height 类 */
@media (min-width: 768px) {
    .md\:min-h-250 {
        min-height: 320px; /* 增加70px：从250px到320px */
    }

    .md\:min-h-300 {
        min-height: 380px; /* 增加80px：从300px到380px */
    }
}

/* 文本截断工具类 */
.line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

/* Markdown 内容样式 */
.markdown-content {
    font-size: var(--text-responsive-base);
    font-family: var(--font-family-sans);
    line-height: var(--line-height-relaxed);
    color: var(--color-text-primary);
}